/* ===== VENDOR DASHBOARD CSS ===== */
/* Dashboard specific styles with light green theme */

/* === WELCOME SECTION === */
.welcome-section {
    background: linear-gradient(135deg, var(--vendor-primary-light), var(--vendor-bg-light));
    border-radius: 12px;
    padding: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-6);
    border: 1px solid var(--vendor-border-light);
    position: relative;
    overflow: hidden;
    max-width: 100%;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: var(--vendor-primary);
    border-radius: 50%;
    opacity: 0.1;
}

.welcome-content {
    position: relative;
    z-index: 1;
}

.welcome-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--vendor-space-2);
    background: var(--vendor-primary);
    color: white;
    padding: var(--vendor-space-2) var(--vendor-space-4);
    border-radius: 20px;
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    margin-bottom: var(--vendor-space-4);
}

.welcome-title {
    font-size: var(--vendor-font-size-xl);
    font-weight: 600;
    color: var(--vendor-text);
    margin-bottom: var(--vendor-space-2);
    line-height: 1.3;
}

.welcome-subtitle {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-light);
    margin-bottom: var(--vendor-space-4);
    line-height: 1.5;
}

.welcome-actions {
    display: flex;
    gap: var(--vendor-space-4);
    flex-wrap: wrap;
}

.welcome-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--vendor-space-2);
    padding: var(--vendor-space-3) var(--vendor-space-5);
    border-radius: 10px;
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.welcome-btn-primary {
    background: var(--vendor-primary);
    color: white;
}

.welcome-btn-primary:hover {
    background: var(--vendor-primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--vendor-shadow-lg);
}

.welcome-btn-secondary {
    background: var(--vendor-surface);
    color: var(--vendor-text);
    border: 1px solid var(--vendor-border);
}

.welcome-btn-secondary:hover {
    background: var(--vendor-surface-hover);
    transform: translateY(-2px);
    box-shadow: var(--vendor-shadow);
}

/* === STATS CARDS === */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--vendor-space-4);
    margin-bottom: var(--vendor-space-6);
}

.stats-card {
    background: var(--vendor-surface);
    border-radius: 10px;
    padding: var(--vendor-space-4);
    border: 1px solid var(--vendor-border-light);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--vendor-shadow-lg);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--stats-color, var(--vendor-primary));
}

.stats-card.sales::before { background: var(--vendor-primary); }
.stats-card.orders::before { background: var(--vendor-teal); }
.stats-card.products::before { background: var(--vendor-primary-dark); }
.stats-card.customers::before { background: #f59e0b; }

.stats-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--vendor-space-3);
}

.stats-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-font-size-lg);
    color: white;
}

.stats-card.sales .stats-icon { background: var(--vendor-primary); }
.stats-card.orders .stats-icon { background: var(--vendor-teal); }
.stats-card.products .stats-icon { background: var(--vendor-primary-dark); }
.stats-card.customers .stats-icon { background: #f59e0b; }

.stats-trend {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-1);
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 6px;
}

.stats-trend.up {
    background: #dcfce7;
    color: #16a34a;
}

.stats-trend.down {
    background: #fef2f2;
    color: #dc2626;
}

.stats-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--vendor-text);
    margin-bottom: var(--vendor-space-1);
    line-height: 1;
}

.stats-label {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    font-weight: 500;
}

/* === DASHBOARD LAYOUT === */
.dashboard-section {
    margin-bottom: var(--vendor-space-6);
}

.dashboard-section:last-child {
    margin-bottom: 0;
}

/* === RECENT ORDERS TABLE === */
.orders-section {
    background: var(--vendor-surface);
    border-radius: 12px;
    border: 1px solid var(--vendor-border-light);
    overflow: hidden;
    margin-bottom: var(--vendor-space-6);
}

.section-header {
    padding: var(--vendor-space-6);
    border-bottom: 1px solid var(--vendor-border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.section-title {
    font-size: var(--vendor-font-size-lg);
    font-weight: 600;
    color: var(--vendor-text);
    margin: 0;
}

.section-action {
    color: var(--vendor-primary);
    text-decoration: none;
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    transition: color 0.2s ease;
}

.section-action:hover {
    color: var(--vendor-primary-dark);
}

.orders-table {
    width: 100%;
    border-collapse: collapse;
}

.orders-table th,
.orders-table td {
    padding: var(--vendor-space-4);
    text-align: left;
    border-bottom: 1px solid var(--vendor-border-light);
}

.orders-table th {
    background: var(--vendor-bg-light);
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.orders-table td {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text);
}

.order-id {
    font-weight: 600;
    color: var(--vendor-primary);
}

.order-status {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.order-status.pending {
    background: #fef3c7;
    color: #d97706;
}

.order-status.processing {
    background: #dbeafe;
    color: #2563eb;
}

.order-status.completed {
    background: #dcfce7;
    color: #16a34a;
}

.order-status.cancelled {
    background: #fef2f2;
    color: #dc2626;
}

/* === PAGINATION === */
.pagination {
    display: flex;
    align-items: center;
    justify-content: between;
    padding: var(--vendor-space-4) var(--vendor-space-6);
    background: var(--vendor-bg-light);
}

.pagination-info {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    margin-left: auto;
}

.pagination-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid var(--vendor-border);
    background: var(--vendor-surface);
    color: var(--vendor-text-muted);
    border-radius: 6px;
    font-size: var(--vendor-font-size-sm);
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: var(--vendor-primary);
    color: white;
    border-color: var(--vendor-primary);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background: var(--vendor-primary);
    color: white;
    border-color: var(--vendor-primary);
}

/* === QUICK ACTIONS === */
.quick-actions-section {
    background: var(--vendor-surface);
    border-radius: 12px;
    border: 1px solid var(--vendor-border-light);
    padding: var(--vendor-space-6);
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--vendor-space-4);
}

.quick-action-card {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-4);
    border-radius: 10px;
    background: var(--vendor-bg-light);
    text-decoration: none;
    color: var(--vendor-text);
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.quick-action-card:hover {
    background: var(--vendor-primary-light);
    border-color: var(--vendor-primary);
    transform: translateX(4px);
}

.quick-action-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-font-size-lg);
    color: white;
    flex-shrink: 0;
}

.quick-action-card:nth-child(1) .quick-action-icon { background: var(--vendor-primary); }
.quick-action-card:nth-child(2) .quick-action-icon { background: var(--vendor-teal); }
.quick-action-card:nth-child(3) .quick-action-icon { background: var(--vendor-primary-dark); }
.quick-action-card:nth-child(4) .quick-action-icon { background: #f59e0b; }

.quick-action-content {
    flex: 1;
}

.quick-action-title {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text);
    margin-bottom: 2px;
}

.quick-action-desc {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    line-height: 1.3;
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
    .welcome-section {
        padding: var(--vendor-space-4);
    }

    .welcome-title {
        font-size: var(--vendor-font-size-lg);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--vendor-space-3);
    }

    .welcome-actions {
        flex-direction: column;
    }

    .welcome-btn {
        justify-content: center;
    }

    .orders-table {
        font-size: var(--vendor-font-size-xs);
    }

    .orders-table th,
    .orders-table td {
        padding: var(--vendor-space-2);
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
