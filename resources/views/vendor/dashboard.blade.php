@extends('layouts.vendor')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')
@section('page-subtitle', 'Welcome back! Here\'s what\'s happening with your store today.')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-dashboard.css') }}">
@endpush

@section('content')
<!-- Welcome Back Section -->
<div class="welcome-section">
    <div class="welcome-content">
        <h1 class="welcome-title">
            Good {{ date('H') < 12 ? 'morning' : (date('H') < 18 ? 'afternoon' : 'evening') }}, {{ Auth::user()->name ?? 'Vendor' }}! 👋
        </h1>
    </div>
</div>

<!-- Stats Cards -->
<div class="stats-grid">
    <div class="stats-card sales">
        <div class="stats-header">
            <div class="stats-icon">
                <i class="fas fa-rupee-sign"></i>
            </div>
            <div class="stats-trend up">
                <i class="fas fa-arrow-up"></i>
                <span>+12.5%</span>
            </div>
        </div>
        <div class="stats-value">₹45,280</div>
        <div class="stats-label">Total Sales</div>
    </div>

    <div class="stats-card orders">
        <div class="stats-header">
            <div class="stats-icon">
                <i class="fas fa-shopping-bag"></i>
            </div>
            <div class="stats-trend up">
                <i class="fas fa-arrow-up"></i>
                <span>+8.2%</span>
            </div>
        </div>
        <div class="stats-value">127</div>
        <div class="stats-label">Total Orders</div>
    </div>

    <div class="stats-card products">
        <div class="stats-header">
            <div class="stats-icon">
                <i class="fas fa-box"></i>
            </div>
            <div class="stats-trend up">
                <i class="fas fa-arrow-up"></i>
                <span>+5.1%</span>
            </div>
        </div>
        <div class="stats-value">89</div>
        <div class="stats-label">Total Products</div>
    </div>

    <div class="stats-card customers">
        <div class="stats-header">
            <div class="stats-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-trend up">
                <i class="fas fa-arrow-up"></i>
                <span>+15.3%</span>
            </div>
        </div>
        <div class="stats-value">342</div>
        <div class="stats-label">Total Customers</div>
    </div>
</div>

<!-- Recent Orders Section -->
<div class="dashboard-section">
    <div class="orders-section">
        <div class="section-header">
            <h2 class="section-title">Recent Orders</h2>
            <a href="{{ route('vendor.orders') }}" class="section-action">View All</a>
        </div>

        <table class="orders-table">
            <thead>
                <tr>
                    <th>Order ID</th>
                    <th>Customer</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><span class="order-id">#WM001</span></td>
                    <td>Rahul Sharma</td>
                    <td>₹1,250</td>
                    <td><span class="order-status completed">Completed</span></td>
                    <td>Today, 2:30 PM</td>
                </tr>
                <tr>
                    <td><span class="order-id">#WM002</span></td>
                    <td>Priya Patel</td>
                    <td>₹890</td>
                    <td><span class="order-status processing">Processing</span></td>
                    <td>Today, 1:15 PM</td>
                </tr>
                <tr>
                    <td><span class="order-id">#WM003</span></td>
                    <td>Amit Kumar</td>
                    <td>₹2,100</td>
                    <td><span class="order-status pending">Pending</span></td>
                    <td>Today, 11:45 AM</td>
                </tr>
                <tr>
                    <td><span class="order-id">#WM004</span></td>
                    <td>Sneha Singh</td>
                    <td>₹675</td>
                    <td><span class="order-status completed">Completed</span></td>
                    <td>Yesterday, 6:20 PM</td>
                </tr>
                <tr>
                    <td><span class="order-id">#WM005</span></td>
                    <td>Vikram Joshi</td>
                    <td>₹1,450</td>
                    <td><span class="order-status cancelled">Cancelled</span></td>
                    <td>Yesterday, 4:10 PM</td>
                </tr>
            </tbody>
        </table>

        <div class="pagination">
            <div class="pagination-info">
                Showing 1 to 5 of 127 orders
            </div>
            <div class="pagination-controls">
                <button class="pagination-btn" disabled>
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="pagination-btn active">1</button>
                <button class="pagination-btn">2</button>
                <button class="pagination-btn">3</button>
                <button class="pagination-btn">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Section -->
<div class="dashboard-section">
    <div class="quick-actions-section">
        <div class="section-header">
            <h2 class="section-title">Quick Actions</h2>
        </div>

        <div class="quick-actions-grid">
            <a href="{{ route('vendor.products.create') }}" class="quick-action-card">
                <div class="quick-action-icon">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="quick-action-content">
                    <div class="quick-action-title">Add Product</div>
                    <div class="quick-action-desc">Create a new product listing</div>
                </div>
            </a>

            <a href="{{ route('vendor.orders') }}" class="quick-action-card">
                <div class="quick-action-icon">
                    <i class="fas fa-shopping-bag"></i>
                </div>
                <div class="quick-action-content">
                    <div class="quick-action-title">Manage Orders</div>
                    <div class="quick-action-desc">View and process orders</div>
                </div>
            </a>

            <a href="{{ route('vendor.customers') }}" class="quick-action-card">
                <div class="quick-action-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="quick-action-content">
                    <div class="quick-action-title">View Customers</div>
                    <div class="quick-action-desc">Manage customer relationships</div>
                </div>
            </a>

            <a href="{{ route('vendor.settings') }}" class="quick-action-card">
                <div class="quick-action-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="quick-action-content">
                    <div class="quick-action-title">Store Settings</div>
                    <div class="quick-action-desc">Configure your store</div>
                </div>
            </a>
        </div>
    </div>
</div>
@endsection
